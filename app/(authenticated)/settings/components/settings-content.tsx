"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { useSearchParams, useRouter, usePathname } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

// Import the component files
import { ProfileSettings } from "./profile-settings"
import { TravelPreferences } from "./travel-preferences"
import { ThemeSettings } from "./theme-settings"
import { AppPreferences } from "./app-preferences"
import { AIPreferences } from "./ai-preferences"
import { NotificationSettings } from "./notification-settings"
import { PrivacySettings } from "./privacy-settings"
import { BillingSettingsWrapper } from "./billing-settings-wrapper"
import { ActivityPreferencesSettings } from "./activity-preferences-settings"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription"

export function SettingsContent() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState("profile")
  const tabsListRef = useRef<HTMLDivElement>(null)
  const isSubscribed = useIsUserSubscribed()

  // Function to update the URL when tab changes
  const handleTabChange = useCallback(
    (value: string) => {
      setActiveTab(value)

      // Create a new URLSearchParams object
      const params = new URLSearchParams(searchParams.toString())
      params.set("tab", value)

      // Update the URL without refreshing the page
      router.replace(`${pathname}?${params.toString()}`, { scroll: false })
    },
    [pathname, router, searchParams]
  )

  // Handle URL parameters for tab selection
  useEffect(() => {
    const tabParam = searchParams.get("tab")
    if (
      tabParam &&
      ["profile", "preferences", "notifications", "privacy", "billing"].includes(tabParam)
    ) {
      setActiveTab(tabParam)

      // Scroll the tab into view on mobile
      setTimeout(() => {
        if (tabsListRef.current) {
          const tabElement = tabsListRef.current.querySelector(`[data-value="${tabParam}"]`)
          if (tabElement) {
            // Scroll the tab into view with some padding
            const container = tabsListRef.current
            const scrollLeft =
              tabElement.getBoundingClientRect().left -
              container.getBoundingClientRect().left +
              container.scrollLeft -
              16 // Add some padding

            container.scrollTo({
              left: scrollLeft,
              behavior: "smooth",
            })
          }
        }
      }, 100) // Small delay to ensure the DOM is updated
    }
  }, [searchParams])

  return (
    <Tabs
      defaultValue="profile"
      className="space-y-4 w-full"
      onValueChange={handleTabChange}
      value={activeTab}
    >
      <div className="w-full overflow-x-auto pb-2 no-scrollbar">
        <TabsList
          className={`inline-flex w-max md:w-full md:grid ${isSubscribed ? "md:grid-cols-6" : "md:grid-cols-5"}`}
          ref={tabsListRef}
        >
          <TabsTrigger value="profile" className="min-w-[100px] md:flex-1 whitespace-nowrap">
            Profile
          </TabsTrigger>
          <TabsTrigger value="preferences" className="min-w-[120px] md:flex-1 whitespace-nowrap">
            Preferences
          </TabsTrigger>
          {isSubscribed && (
            <TabsTrigger
              value="activity-preferences"
              className="min-w-[160px] md:flex-1 whitespace-nowrap"
            >
              Activity Preferences
            </TabsTrigger>
          )}
          <TabsTrigger value="notifications" className="min-w-[140px] md:flex-1 whitespace-nowrap">
            Notifications
          </TabsTrigger>
          <TabsTrigger value="privacy" className="min-w-[160px] md:flex-1 whitespace-nowrap">
            Privacy & Security
          </TabsTrigger>
          <TabsTrigger value="billing" className="min-w-[100px] md:flex-1 whitespace-nowrap">
            Billing
          </TabsTrigger>
        </TabsList>
      </div>

      <TabsContent value="profile" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <ProfileSettings />
        <TravelPreferences />
      </TabsContent>

      <TabsContent value="preferences" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <ThemeSettings />
        <AppPreferences />
        <AIPreferences />
      </TabsContent>

      {isSubscribed && (
        <TabsContent
          value="activity-preferences"
          className="space-y-4 w-full max-w-full overflow-x-hidden"
        >
          <ActivityPreferencesSettings />
        </TabsContent>
      )}

      <TabsContent value="notifications" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <NotificationSettings />
      </TabsContent>

      <TabsContent value="privacy" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <PrivacySettings />
      </TabsContent>

      <TabsContent value="billing" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <BillingSettingsWrapper />
      </TabsContent>
    </Tabs>
  )
}
