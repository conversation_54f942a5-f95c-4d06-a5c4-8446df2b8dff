"use client"

import { useState, useEffect, useCallback } from "react"
import { useSearchParams } from "next/navigation"
import { toast } from "@/components/ui/use-toast"
import { BillingSettings as OriginalBillingSettings } from "./billing-settings"
import { ActivityPreferencesWelcomeModal } from "./activity-preferences-welcome-modal"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription"
import { useActivityPreferences } from "@/lib/domains/activity-preferences"

export function BillingSettingsWrapper() {
  const searchParams = useSearchParams()
  const [toastShown, setToastShown] = useState(false)
  const [showWelcomeModal, setShowWelcomeModal] = useState(false)
  const isSubscribed = useIsUserSubscribed()
  const { preferences } = useActivityPreferences()

  // Check for successful checkout session and handle welcome flow
  useEffect(() => {
    const sessionId = searchParams.get("session_id")
    const canceled = searchParams.get("canceled")

    // Only show toast if we haven't shown it yet for this session
    if (!toastShown) {
      if (sessionId && isSubscribed) {
        // Check if this is a new Pro subscriber (no activity preferences set)
        const isNewProSubscriber =
          !preferences ||
          (!preferences.eateries?.cuisineTypes?.length &&
            !preferences.shopping?.style?.length &&
            !preferences.entertainment?.venues?.length)

        if (isNewProSubscriber) {
          // Check if user has already seen the welcome flow
          const hasSeenWelcome =
            typeof window !== "undefined" &&
            localStorage.getItem("activity-preferences-welcome-shown") === "true"

          if (!hasSeenWelcome) {
            // Show welcome modal for new Pro subscribers
            setShowWelcomeModal(true)
          } else {
            // Show regular success toast if they've already seen the welcome
            toast({
              title: "Subscription successful!",
              description: "Your subscription has been activated.",
            })
          }
        } else {
          // Show regular success toast for returning subscribers
          toast({
            title: "Subscription successful!",
            description: "Your subscription has been activated.",
          })
        }
        setToastShown(true)
      } else if (canceled) {
        toast({
          title: "Subscription canceled",
          description: "Your subscription process was canceled.",
          variant: "destructive",
        })
        setToastShown(true)
      }
    }
  }, [searchParams, toastShown, isSubscribed, preferences])

  // Handle welcome modal close
  const handleWelcomeModalClose = useCallback(() => {
    setShowWelcomeModal(false)
    // Mark that user has seen the welcome flow
    if (typeof window !== "undefined") {
      localStorage.setItem("activity-preferences-welcome-shown", "true")
    }
    // Show success toast after modal is closed
    toast({
      title: "Subscription successful!",
      description: "Your subscription has been activated.",
    })
  }, [])

  return (
    <>
      <OriginalBillingSettings />
      <ActivityPreferencesWelcomeModal
        isOpen={showWelcomeModal}
        onClose={handleWelcomeModalClose}
      />
    </>
  )
}
