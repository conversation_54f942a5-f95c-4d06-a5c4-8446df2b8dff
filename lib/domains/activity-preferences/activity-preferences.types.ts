import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Eateries preferences
 */
export interface EateriesPreferences {
  cuisineTypes: string[]
  diningExperience: string[]
  dietaryNeeds: string[]
}

/**
 * Shopping preferences
 */
export interface ShoppingPreferences {
  style: string[]
  budget: string
  focusAreas: string[]
}

/**
 * Entertainment preferences
 */
export interface EntertainmentPreferences {
  venues: string[]
  vibe: string[]
  interests: string[]
}

/**
 * Activity preferences entity
 */
export interface ActivityPreferences extends BaseEntity {
  userId: string
  eateries: EateriesPreferences
  shopping: ShoppingPreferences
  entertainment: EntertainmentPreferences
}

/**
 * Activity preferences create data (without BaseEntity fields)
 */
export interface ActivityPreferencesCreateData {
  userId: string
  eateries: EateriesPreferences
  shopping: ShoppingPreferences
  entertainment: EntertainmentPreferences
}

/**
 * Activity preferences update data (partial)
 */
export interface ActivityPreferencesUpdateData {
  eateries?: Partial<EateriesPreferences>
  shopping?: Partial<ShoppingPreferences>
  entertainment?: Partial<EntertainmentPreferences>
}

/**
 * Default activity preferences structure
 */
export const DEFAULT_ACTIVITY_PREFERENCES: Omit<ActivityPreferences, keyof BaseEntity | "userId"> =
  {
    eateries: {
      cuisineTypes: [],
      diningExperience: [],
      dietaryNeeds: [],
    },
    shopping: {
      style: [],
      budget: "mid-range",
      focusAreas: [],
    },
    entertainment: {
      venues: [],
      vibe: [],
      interests: [],
    },
  }

/**
 * Available preference options
 */
export const PREFERENCE_OPTIONS = {
  eateries: {
    cuisineTypes: [
      "Mexican",
      "Italian",
      "Vegan",
      "Seafood",
      "Street Food",
      "Asian Fusion",
      "American",
    ],
    diningExperience: [
      "Fine Dining",
      "Local Gems",
      "Budget-Friendly",
      "Rooftop / Scenic Views",
      "Fast Casual",
    ],
    dietaryNeeds: ["Vegetarian", "Gluten-Free", "Halal", "Kosher", "None"],
  },
  shopping: {
    style: ["Boutiques", "High-End / Luxury", "Local Markets", "Malls", "Thrift / Vintage"],
    budget: ["budget", "mid-range", "high-end"],
    focusAreas: ["Fashion", "Tech & Gadgets", "Souvenirs", "Home Goods", "Art & Decor"],
  },
  entertainment: {
    venues: [
      "Live Music",
      "Theater / Performing Arts",
      "Comedy Clubs",
      "Nightclubs",
      "Outdoor Events",
    ],
    vibe: ["Chill & Relaxing", "Trendy & Upscale", "High-Energy", "Family-Friendly"],
    interests: [
      "Local Shows",
      "Cultural Performances",
      "DJ Sets",
      "Festivals",
      "Trivia / Game Nights",
    ],
  },
} as const
