"use client"

import { useEffect, useMemo, useCallback } from "react"
import { useActivityPreferencesStore } from "./activity-preferences.store"
import { useUser } from "../auth/auth.hooks"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"
import { ActivityPreferencesUpdateData } from "./activity-preferences.types"

// Export real-time hooks
export * from "./activity-preferences.realtime.hooks"

/**
 * Hook to get activity preferences with subscription validation
 * @param userId Optional user ID (defaults to current user)
 * @param useRealtime Whether to use real-time subscription
 * @returns Activity preferences hook return object
 */
export const useActivityPreferences = (userId?: string, useRealtime: boolean = false) => {
  const authUser = useUser()
  const isSubscribed = useIsUserSubscribed()
  const uid = userId || authUser?.uid || ""

  const {
    preferences,
    loading,
    error,
    fetchActivityPreferences,
    updateActivityPreferences,
    subscribeToActivityPreferences,
    unsubscribeFromActivityPreferences,
  } = useActivityPreferencesStore()

  // Memoize subscription status to prevent infinite re-renders
  const memoizedIsSubscribed = useMemo(() => isSubscribed, [isSubscribed])
  
  // Memoize userId to prevent infinite re-renders
  const memoizedUserId = useMemo(() => uid, [uid])

  useEffect(() => {
    if (memoizedUserId && memoizedIsSubscribed) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToActivityPreferences(memoizedUserId)

        // Cleanup on unmount
        return () => {
          unsubscribeFromActivityPreferences()
        }
      } else {
        // Use regular fetch
        fetchActivityPreferences(memoizedUserId)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [
    memoizedUserId,
    memoizedIsSubscribed,
    useRealtime,
    fetchActivityPreferences,
    subscribeToActivityPreferences,
    unsubscribeFromActivityPreferences,
  ])

  // Create convenience update function with memoization
  const updatePreferences = useCallback(
    async (preferencesData: ActivityPreferencesUpdateData) => {
      if (!memoizedUserId || !memoizedIsSubscribed) return false
      return updateActivityPreferences(memoizedUserId, preferencesData)
    },
    [memoizedUserId, memoizedIsSubscribed, updateActivityPreferences]
  )

  // Memoize the preferences to return null for non-Pro users
  const memoizedPreferences = useMemo(() => {
    if (!memoizedIsSubscribed) return null
    return preferences
  }, [memoizedIsSubscribed, preferences])

  return {
    preferences: memoizedPreferences,
    loading: memoizedIsSubscribed ? loading : false,
    error: memoizedIsSubscribed ? error : null,
    updatePreferences,
    isSubscribed: memoizedIsSubscribed,
  }
}

/**
 * Hook specifically for updating activity preferences
 * @param userId Optional user ID (defaults to current user)
 * @returns Update function and loading state
 */
export const useUpdateActivityPreferences = (userId?: string) => {
  const authUser = useUser()
  const isSubscribed = useIsUserSubscribed()
  const uid = userId || authUser?.uid || ""

  const { updateActivityPreferences, loading } = useActivityPreferencesStore()

  // Memoize subscription status and userId
  const memoizedIsSubscribed = useMemo(() => isSubscribed, [isSubscribed])
  const memoizedUserId = useMemo(() => uid, [uid])

  const updatePreferences = useCallback(
    async (preferencesData: ActivityPreferencesUpdateData) => {
      if (!memoizedUserId || !memoizedIsSubscribed) {
        throw new Error("Activity preferences are only available for Pro subscribers")
      }
      return updateActivityPreferences(memoizedUserId, preferencesData)
    },
    [memoizedUserId, memoizedIsSubscribed, updateActivityPreferences]
  )

  return {
    updatePreferences,
    loading: memoizedIsSubscribed ? loading : false,
    isSubscribed: memoizedIsSubscribed,
  }
}
