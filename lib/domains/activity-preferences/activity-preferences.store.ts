import { create } from "zustand"
import { ActivityPreferences, ActivityPreferencesUpdateData } from "./activity-preferences.types"
import { ActivityPreferencesService } from "./activity-preferences.service"
import { ActivityPreferencesRealtimeService } from "./activity-preferences.realtime.service"

/**
 * Activity preferences store state interface
 */
interface ActivityPreferencesState {
  // State
  preferences: ActivityPreferences | null
  loading: boolean
  error: Error | null

  // Real-time subscriptions
  preferencesSubscription: (() => void) | null

  // Actions
  fetchActivityPreferences: (userId: string) => Promise<void>
  updateActivityPreferences: (
    userId: string,
    preferencesData: ActivityPreferencesUpdateData
  ) => Promise<boolean>
  subscribeToActivityPreferences: (userId: string) => void
  unsubscribeFromActivityPreferences: () => void
  clearActivityPreferences: () => void
}

/**
 * Activity preferences store with Zustand
 */
export const useActivityPreferencesStore = create<ActivityPreferencesState>()((set, get) => ({
  // Initial state
  preferences: null,
  loading: false,
  error: null,
  preferencesSubscription: null,

  // Actions
  fetchActivityPreferences: async (userId: string) => {
    set({ loading: true, error: null })

    try {
      const preferences = await ActivityPreferencesService.getActivityPreferences(userId)
      set({ preferences, loading: false })
    } catch (error) {
      console.error("Error fetching activity preferences:", error)
      set({ error: error as Error, loading: false })
    }
  },

  updateActivityPreferences: async (
    userId: string,
    preferencesData: ActivityPreferencesUpdateData
  ): Promise<boolean> => {
    set({ loading: true, error: null })

    try {
      const success = await ActivityPreferencesService.updateActivityPreferences(
        userId,
        preferencesData
      )

      if (success) {
        // Refresh preferences after update
        const updatedPreferences = await ActivityPreferencesService.getActivityPreferences(userId)
        set({ preferences: updatedPreferences, loading: false })
      } else {
        set({ loading: false })
      }

      return success
    } catch (error) {
      console.error("Error updating activity preferences:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  subscribeToActivityPreferences: (userId: string) => {
    // Unsubscribe from any existing subscription
    const { unsubscribeFromActivityPreferences } = get()
    unsubscribeFromActivityPreferences()

    set({ loading: true, error: null })

    // Subscribe to the activity preferences
    const unsubscribe = ActivityPreferencesRealtimeService.subscribeToActivityPreferences(
      userId,
      (preferences, error) => {
        if (error) {
          set({ error, loading: false })
          return
        }

        set({ preferences, loading: false })
      }
    )

    set({ preferencesSubscription: unsubscribe })
  },

  unsubscribeFromActivityPreferences: () => {
    const { preferencesSubscription } = get()
    if (preferencesSubscription) {
      preferencesSubscription()
      set({ preferencesSubscription: null })
    }
  },

  clearActivityPreferences: () => {
    const { unsubscribeFromActivityPreferences } = get()
    unsubscribeFromActivityPreferences()
    set({
      preferences: null,
      loading: false,
      error: null,
      preferencesSubscription: null,
    })
  },
}))
