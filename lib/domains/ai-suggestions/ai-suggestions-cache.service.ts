"use client"

import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import { GooglePlaceImage } from "../trip/trip.types"

// Define the localStorage key prefix
const AI_SUGGESTIONS_CACHE_KEY_PREFIX = "togeda-ai-suggestions"
const NEW_USER_TRIP_CACHE_KEY = "togeda-new-user-trip-cache"
const SUGGESTION_HISTORY_KEY_PREFIX = "togeda-suggestion-history"

// Types for cached suggestions
export interface CachedTaskSuggestion {
  title: string
  description: string
  category?: string
  priority?: string
  tags?: string[]
  affiliateLinks?: Array<{
    url: string
    title: string
    description: string
    provider?: string
    tags?: string[]
  }>
  // Legacy support for single affiliate link (will be deprecated)
  affiliateLink?: {
    url: string
    title: string
    description: string
    provider?: string
    tags?: string[]
  } | null
}

export interface CachedTripSuggestion {
  destination: string
  description: string
  tags: string[]
  budget: string
  /** @deprecated Use googlePlaceImage instead */
  image?: string
  /** Google Place image reference for persistent image URLs */
  googlePlaceImage?: GooglePlaceImage
  /** @deprecated Use googlePlaceImage.placeId instead */
  placeId?: string
  formattedAddress?: string
  attribution?: {
    name: string
    photoReference?: string
    username?: string
    link?: string
  }
}

export interface CachedItinerarySuggestion {
  title: string
  description: string
  day: number
  timeOfDay: "morning" | "afternoon" | "evening"
  duration: string
  tags?: string[]
  affiliateLinks?: Array<{
    url: string
    title: string
    description: string
    provider?: string
    tags?: string[]
  }>
  // Legacy support for single affiliate link (will be deprecated)
  affiliateLink?: {
    url: string
    title: string
    description: string
    provider?: string
    tags?: string[]
  } | null
}

// Type for suggestion history tracking
export interface SuggestionHistoryEntry {
  title: string
  timestamp: number
  tripId: string
  day?: number
}

// Type for the cache entry
interface CacheEntry<T> {
  data: T[]
  timestamp: number
  contextId: string // tripId or squadId
  userId: string
}

/**
 * AI Suggestions Cache Service
 * Handles caching AI-generated suggestions in localStorage
 */
export class AISuggestionsCacheService {
  /**
   * Generate a cache key for a specific category and context
   */
  private static getCacheKey(category: AIUsageCategory, contextId: string): string {
    return `${AI_SUGGESTIONS_CACHE_KEY_PREFIX}-${category}-${contextId}`
  }

  /**
   * Save suggestions to localStorage
   */
  static saveSuggestions<T>(
    category: AIUsageCategory,
    contextId: string,
    userId: string,
    suggestions: T[]
  ): void {
    if (typeof window === "undefined") return

    const cacheKey = this.getCacheKey(category, contextId)
    const cacheEntry: CacheEntry<T> = {
      data: suggestions,
      timestamp: Date.now(),
      contextId,
      userId,
    }

    try {
      localStorage.setItem(cacheKey, JSON.stringify(cacheEntry))
    } catch (error) {
      console.error("Error saving suggestions to localStorage:", error)
    }
  }

  /**
   * Get suggestions from localStorage
   */
  static getSuggestions<T>(
    category: AIUsageCategory,
    contextId: string,
    userId: string
  ): T[] | null {
    if (typeof window === "undefined") return null

    const cacheKey = this.getCacheKey(category, contextId)

    try {
      const cachedData = localStorage.getItem(cacheKey)
      if (!cachedData) return null

      const cacheEntry = JSON.parse(cachedData) as CacheEntry<T>

      // Only return suggestions if they belong to the same user
      if (cacheEntry.userId !== userId) return null

      // Only return suggestions if they're for the same context
      if (cacheEntry.contextId !== contextId) return null

      return cacheEntry.data
    } catch (error) {
      console.error("Error retrieving suggestions from localStorage:", error)
      return null
    }
  }

  /**
   * Check if cached suggestions exist
   */
  static hasCachedSuggestions(
    category: AIUsageCategory,
    contextId: string,
    userId: string
  ): boolean {
    if (typeof window === "undefined") return false

    const cacheKey = this.getCacheKey(category, contextId)

    try {
      const cachedData = localStorage.getItem(cacheKey)
      if (!cachedData) return false

      const cacheEntry = JSON.parse(cachedData) as CacheEntry<any>

      // Only consider suggestions if they belong to the same user
      if (cacheEntry.userId !== userId) return false

      // Only consider suggestions if they're for the same context
      if (cacheEntry.contextId !== contextId) return false

      return Array.isArray(cacheEntry.data) && cacheEntry.data.length > 0
    } catch (error) {
      console.error("Error checking for cached suggestions:", error)
      return false
    }
  }

  /**
   * Clear cached suggestions
   */
  static clearSuggestions(category: AIUsageCategory, contextId: string): void {
    if (typeof window === "undefined") return

    const cacheKey = this.getCacheKey(category, contextId)

    try {
      localStorage.removeItem(cacheKey)
    } catch (error) {
      console.error("Error clearing cached suggestions:", error)
    }
  }

  /**
   * Clear all cached suggestions
   */
  static clearAllSuggestions(): void {
    if (typeof window === "undefined") return

    try {
      Object.keys(localStorage).forEach((key) => {
        if (key.startsWith(AI_SUGGESTIONS_CACHE_KEY_PREFIX)) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.error("Error clearing all cached suggestions:", error)
    }
  }

  // New User Trip Cache Methods

  /**
   * Save new user trip suggestions to localStorage
   */
  static saveNewUserTripSuggestions(userId: string, suggestions: CachedTripSuggestion[]): void {
    if (typeof window === "undefined") return

    const cacheKey = `${NEW_USER_TRIP_CACHE_KEY}-${userId}`
    const cacheEntry = {
      data: suggestions,
      timestamp: Date.now(),
      userId,
    }

    try {
      localStorage.setItem(cacheKey, JSON.stringify(cacheEntry))
    } catch (error) {
      console.error("Error saving new user trip suggestions to localStorage:", error)
    }
  }

  /**
   * Get new user trip suggestions from localStorage
   */
  static getNewUserTripSuggestions(userId: string): CachedTripSuggestion[] | null {
    if (typeof window === "undefined") return null

    const cacheKey = `${NEW_USER_TRIP_CACHE_KEY}-${userId}`

    try {
      const cachedData = localStorage.getItem(cacheKey)
      if (!cachedData) return null

      const cacheEntry = JSON.parse(cachedData)

      // Only return suggestions if they belong to the same user
      if (cacheEntry.userId !== userId) return null

      return cacheEntry.data
    } catch (error) {
      console.error("Error retrieving new user trip suggestions from localStorage:", error)
      return null
    }
  }

  /**
   * Check if new user trip suggestions exist
   */
  static hasNewUserTripSuggestions(userId: string): boolean {
    if (typeof window === "undefined") return false

    const cacheKey = `${NEW_USER_TRIP_CACHE_KEY}-${userId}`

    try {
      const cachedData = localStorage.getItem(cacheKey)
      if (!cachedData) return false

      const cacheEntry = JSON.parse(cachedData)

      // Only consider suggestions if they belong to the same user
      if (cacheEntry.userId !== userId) return false

      return Array.isArray(cacheEntry.data) && cacheEntry.data.length > 0
    } catch (error) {
      console.error("Error checking for new user trip suggestions:", error)
      return false
    }
  }

  /**
   * Clear new user trip suggestions
   */
  static clearNewUserTripSuggestions(userId: string): void {
    if (typeof window === "undefined") return

    const cacheKey = `${NEW_USER_TRIP_CACHE_KEY}-${userId}`

    try {
      localStorage.removeItem(cacheKey)
    } catch (error) {
      console.error("Error clearing new user trip suggestions:", error)
    }
  }

  /**
   * Transfer new user trip suggestions to squad-bound cache
   */
  static transferNewUserTripSuggestionsToSquad(
    userId: string,
    squadId: string
  ): CachedTripSuggestion[] | null {
    if (typeof window === "undefined") return null

    try {
      // Get new user suggestions
      const newUserSuggestions = this.getNewUserTripSuggestions(userId)
      if (!newUserSuggestions || newUserSuggestions.length === 0) return null

      // Save to squad-bound cache
      this.saveSuggestions<CachedTripSuggestion>(
        AIUsageCategory.TRIP,
        squadId,
        userId,
        newUserSuggestions
      )

      // Clear new user cache
      this.clearNewUserTripSuggestions(userId)

      return newUserSuggestions
    } catch (error) {
      console.error("Error transferring new user trip suggestions to squad:", error)
      return null
    }
  }

  // Suggestion History Methods

  /**
   * Get suggestion history key
   */
  private static getSuggestionHistoryKey(tripId: string, day?: number): string {
    return `${SUGGESTION_HISTORY_KEY_PREFIX}-${tripId}${day ? `-day-${day}` : ""}`
  }

  /**
   * Add suggestions to history
   */
  static addSuggestionsToHistory(tripId: string, suggestions: string[], day?: number): void {
    if (typeof window === "undefined") return

    const historyKey = this.getSuggestionHistoryKey(tripId, day)
    const timestamp = Date.now()

    try {
      const existingHistory = this.getSuggestionHistory(tripId, day)
      const newEntries: SuggestionHistoryEntry[] = suggestions.map((title) => ({
        title,
        timestamp,
        tripId,
        day,
      }))

      const updatedHistory = [...existingHistory, ...newEntries]

      // Keep only the last 10 suggestions and clean up old ones (older than 24 hours)
      const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000
      const filteredHistory = updatedHistory
        .filter((entry) => entry.timestamp > oneDayAgo)
        .slice(-10)

      localStorage.setItem(historyKey, JSON.stringify(filteredHistory))
    } catch (error) {
      console.error("Error adding suggestions to history:", error)
    }
  }

  /**
   * Get recent suggestions from history
   */
  static getSuggestionHistory(tripId: string, day?: number): SuggestionHistoryEntry[] {
    if (typeof window === "undefined") return []

    const historyKey = this.getSuggestionHistoryKey(tripId, day)

    try {
      const historyData = localStorage.getItem(historyKey)
      if (!historyData) return []

      const history = JSON.parse(historyData) as SuggestionHistoryEntry[]

      // Filter out old entries (older than 24 hours)
      const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000
      return history.filter((entry) => entry.timestamp > oneDayAgo)
    } catch (error) {
      console.error("Error retrieving suggestion history:", error)
      return []
    }
  }

  /**
   * Get recent suggestion titles for deduplication
   */
  static getRecentSuggestionTitles(tripId: string, day?: number, count: number = 3): string[] {
    const history = this.getSuggestionHistory(tripId, day)
    return history
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, count)
      .map((entry) => entry.title)
  }

  /**
   * Clear suggestion history for a trip
   */
  static clearSuggestionHistory(tripId: string, day?: number): void {
    if (typeof window === "undefined") return

    const historyKey = this.getSuggestionHistoryKey(tripId, day)

    try {
      localStorage.removeItem(historyKey)
    } catch (error) {
      console.error("Error clearing suggestion history:", error)
    }
  }
}
